#include <Windows.h>
#include <ShlObj.h>
#include <filesystem>
#include <algorithm>
#include <fstream>
#include <sstream>
#include <chrono>
#include <array>
#include <iomanip>
#include <ctime>
#include "LicenseSerial.h"
#include "License.h"

void License::EnsureAvailability() noexcept {
    try {
        // Get %ProgramData%\Source Insight\4.0\si4.lic file path, create parent directory if not exists
        std::array<wchar_t, MAX_PATH> commonAppDataPath;
        if (SHGetFolderPathW(nullptr, CSIDL_COMMON_APPDATA, nullptr, 0, commonAppDataPath.data()) != S_OK) {
            throw std::runtime_error("Failed to get CSIDL_COMMON_APPDATA path, error code: " + std::to_string(GetLastError()));
        }
        std::filesystem::path licenseFilePath = std::filesystem::path(commonAppDataPath.data()) / L"Source Insight" / L"4.0" / L"si4.lic";
        std::filesystem::create_directories(licenseFilePath.parent_path());

        // If License file does not exist or does not contain SourceInsight4Patch string, generate a new one
        std::wfstream licenseFile;
        licenseFile.exceptions(std::ifstream::failbit | std::ifstream::badbit);
        if (std::filesystem::exists(licenseFilePath)) {
            licenseFile.open(licenseFilePath, std::ios::in);
            const std::wstring magic = L"SourceInsight4Patch";
            std::wstring fileContent((std::istreambuf_iterator<wchar_t>(licenseFile)), std::istreambuf_iterator<wchar_t>());
            if (fileContent.find(magic) != std::wstring::npos) {
                licenseFile.close();
                return;
            }
            licenseFile.close();
        }
        licenseFile.open(licenseFilePath, std::ios::out | std::ios::trunc);
        LicenseSerial licenseSerial;
        std::chrono::time_point<std::chrono::system_clock> now = std::chrono::system_clock::now();
        std::time_t now_time_t = std::chrono::system_clock::to_time_t(now);
        std::tm now_tm;
        localtime_s(&now_tm, &now_time_t);

        std::wstringstream licenseContent;
        licenseContent << L"<SourceInsightLicense>\n"
                       << L"    <LicenseProperties ActId=\"\"\n"
                       << L"                       HWID=\"\"\n"
                       << L"                       Serial=\"" << licenseSerial.Generate() << L"\"\n"
                       << L"                       LicensedUser=\"SourceInsight4Patch\"\n"
                       << L"                       Organization=\"Fox\"\n"
                       << L"                       Email=\"\"\n"
                       << L"                       Type=\"Standard\"\n"
                       << L"                       Version=\"4\"\n"
                       << L"                       MinorVersion=\"0\"\n"
                       << L"                       Date=\"" << std::put_time(&now_tm, L"%Y-%m-%d") << L"\" />\n"
                       << L"    <Signature Value=\"SourceInsight4Patch\" />\n"
                       << L"</SourceInsightLicense>";
        licenseFile << licenseContent.str();
        licenseFile.close();
    } catch (const std::exception& exception) {
        MessageBoxW(nullptr, (std::wstringstream() << L"Failed to generate license file: " << exception.what()).str().data(), L"License Generation Failed", MB_ICONSTOP);
    }
}
