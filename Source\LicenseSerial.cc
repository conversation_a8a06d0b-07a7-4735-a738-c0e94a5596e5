#include "LicenseSerial.h"

std::array<uint8_t, 256> LicenseSerial::substitutionTable = {
    0x23 ,0xDD ,0x78 ,0xB5 ,0x33 ,0x6F ,0xD4 ,0xF9 ,0xA6 ,0xE8 ,0xCC ,0x7C ,0x9F ,0xB3 ,0x22 ,0xDA,
    0x32 ,0xDF ,0x71 ,0xB7 ,0x61 ,0x3D ,0x6B ,0x57 ,0xD7 ,0xA1 ,0x34 ,0x38 ,0xF2 ,0xE1 ,0xF3 ,0xB8,
    0x1A ,0x80 ,0xF5 ,0xFE ,0x91 ,0x01 ,0x3C ,0x73 ,0x93 ,0x48 ,0xA0 ,0xE0 ,0x94 ,0xAA ,0x39 ,0x8F,
    0x58 ,0xE2 ,0x31 ,0x0B ,0xBB ,0xCE ,0x4C ,0xD2 ,0x56 ,0xC2 ,0x5E ,0x27 ,0xB6 ,0xFB ,0x65 ,0xAE,
    0x55 ,0x60 ,0xBD ,0x10 ,0x86 ,0xF7 ,0xC1 ,0x88 ,0x12 ,0xED ,0x67 ,0xC4 ,0x74 ,0x30 ,0x1B ,0xBC,
    0x9A ,0xB0 ,0xEF ,0x36 ,0xC5 ,0x72 ,0x5B ,0x7E ,0x54 ,0x2C ,0x0F ,0xF6 ,0xA9 ,0x85 ,0x2A ,0xB1,
    0x37 ,0xF1 ,0x2F ,0x4E ,0xE7 ,0x6A ,0x75 ,0xA8 ,0x26 ,0xEB ,0x3F ,0x6C ,0x69 ,0x20 ,0x87 ,0x62,
    0x8D ,0x68 ,0xA5 ,0xFA ,0x3A ,0x04 ,0x21 ,0x1F ,0xAC ,0x05 ,0xA4 ,0x76 ,0x11 ,0x70 ,0x9E ,0x46,
    0x24 ,0x5D ,0xC6 ,0xE4 ,0x95 ,0x82 ,0x1C ,0xBA ,0x59 ,0x09 ,0xD9 ,0x44 ,0x98 ,0x92 ,0x07 ,0xAF,
    0xA7 ,0x41 ,0x96 ,0x90 ,0xB4 ,0x42 ,0x63 ,0x99 ,0xD0 ,0x4D ,0x97 ,0xBE ,0x40 ,0xCF ,0x84 ,0xE5,
    0x1D ,0x5A ,0x0C ,0x7F ,0xC7 ,0xEA ,0xEE ,0xEC ,0x00 ,0xD5 ,0x49 ,0x2D ,0x51 ,0xAD ,0xB9 ,0x89,
    0x77 ,0x52 ,0x3E ,0x8C ,0xE6 ,0xFF ,0x15 ,0xDE ,0x6D ,0x14 ,0xA2 ,0xCD ,0xA3 ,0xD6 ,0x17 ,0x81,
    0xC8 ,0x45 ,0x4B ,0x35 ,0x0A ,0x0D ,0xFC ,0x9D ,0x16 ,0x3B ,0xD3 ,0x7D ,0xD1 ,0xF4 ,0xFD ,0xCA,
    0x25 ,0x06 ,0x6E ,0xF8 ,0x5F ,0xBF ,0x8A ,0x7B ,0x50 ,0xD8 ,0x79 ,0x9C ,0xAB ,0x43 ,0x53 ,0xCB,
    0x8E ,0x4F ,0xE3 ,0xC9 ,0x8B ,0xDC ,0x5C ,0xC0 ,0x1E ,0x9B ,0x18 ,0x02 ,0x47 ,0x03 ,0x2B ,0x0E,
    0x66 ,0x4A ,0xB2 ,0xF0 ,0xE9 ,0x19 ,0x29 ,0x7A ,0xC3 ,0x08 ,0x83 ,0xDB ,0x64 ,0x13 ,0x2E ,0x28,
};

std::array<uint8_t, 26> LicenseSerial::alphabetTable = {
    0x4B, 0x56, 0x39, 0x36, 0x47, 0x4D, 0x4A, 0x59, 0x48, 0x37, 0x51, 0x46, 0x35,
    0x54, 0x43, 0x57, 0x34, 0x55, 0x33, 0x58, 0x5A, 0x50, 0x52, 0x53, 0x44, 0x4E,
};

wchar_t LicenseSerial::GenerateRandomLetterOrDigit() noexcept {
    std::uniform_int_distribution<uint16_t> distribution(0, 35);
    uint16_t randomValue = distribution(randomEngine);
    if (randomValue < 26) {
        return L'A' + randomValue;
    } else {
        return L'0' + randomValue - 26;
    }
}

wchar_t LicenseSerial::GenerateRandomRGDF() noexcept {
    std::uniform_int_distribution<uint16_t> distribution(0, 3);
    switch (distribution(randomEngine)) {
        case 0:
            return L'R';
        case 1:
            return L'G';
        case 2:
            return L'D';
        default:
            return L'F';
    }
}

LicenseSerial::LicenseSerial() noexcept : randomEngine(randomDevice()) {}

std::wstring LicenseSerial::Generate() noexcept {
    std::wstring serial(19, L'-');
    wchar_t* serialData = serial.data();
    serialData[0] = L'S';
    serialData[1] = L'4';
    serialData[2] = L'S';
    serialData[3] = L'G';
    serialData[5] = GenerateRandomLetterOrDigit();
    serialData[6] = GenerateRandomRGDF();
    serialData[7] = GenerateRandomLetterOrDigit();
    serialData[8] = GenerateRandomLetterOrDigit();
    for (size_t i = 0; i < 4; i++) {
        serialData[i + 10] = GenerateRandomLetterOrDigit();
    }
    for (size_t i = 0; i < 4; i++) {
        uint8_t hash = substitutionTable[serialData[0] + i];
        for (size_t j = 1; j < 15; j++) {
            hash = substitutionTable[serialData[j] ^ hash];
        }
        serialData[i + 15] = alphabetTable[hash % 26];
    }
    return serial;
}
