﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Source\Detours\Detours.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\ProxyLibrary\ProxyLibrary.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\ProxyLibrary\ProxyLibraryGlobals.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Patch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\LicenseSerial.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\License.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Source\Detours\Detours.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Source\Detours\DisAsm.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Source\DllMain.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Source\Detours\Modules.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Source\ProxyLibrary\ProxyLibrary.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Source\ProxyLibrary\ProxyLibraryGlobals.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Source\Patch.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Source\LicenseSerial.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Source\License.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Source\ProxyLibrary\ProxyLibraryExports.def">
      <Filter>Source Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <MASM Include="Source\ProxyLibrary\ProxyLibraryExports.asm">
      <Filter>Source Files</Filter>
    </MASM>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="Source\Resources\Resources.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Xml Include="Source\Resources\Manifest.xml">
      <Filter>Resource Files</Filter>
    </Xml>
  </ItemGroup>
</Project>