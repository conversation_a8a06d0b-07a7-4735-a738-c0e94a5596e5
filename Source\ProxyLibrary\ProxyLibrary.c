// Generated by ProxyPESourceGenerator

#include "ProxyLibrary.h"
#include "ProxyLibraryGlobals.h"
#include <strsafe.h>

static HMODULE module = NULL;

static void PASCAL LoadOriginalLibrary(LPCSTR libraryFilePath) {
    if (module != NULL) {
        return;
    }
    HANDLE processHeap = GetProcessHeap();
    do {
        DWORD expandedLibraryFilePathSize = ExpandEnvironmentStringsA(libraryFilePath, NULL, 0);
        if (expandedLibraryFilePathSize == 0) {
            break;
        }
        LPSTR expandedLibraryFilePath = (LPSTR) HeapAlloc(processHeap, 0, expandedLibraryFilePathSize);
        if (expandedLibraryFilePath == NULL) {
            break;
        }
        expandedLibraryFilePathSize = ExpandEnvironmentStringsA(libraryFilePath, expandedLibraryFilePath, expandedLibraryFilePathSize);
        if (expandedLibraryFilePathSize == 0) {
            break;
        }
        module = LoadLibraryA(expandedLibraryFilePath);
        HeapFree(processHeap, 0, expandedLibraryFilePath);
        if (module != NULL) {
            return;
        }
    } while (0);
    do {
        SIZE_T libraryNameLength = 0;
        if (FAILED(StringCchLengthA(libraryFilePath, STRSAFE_MAX_CCH, &libraryNameLength))) {
            break;
        }
        LPSTR errorMessage = (LPSTR) HeapAlloc(processHeap, 0, libraryNameLength + 16);
        if (errorMessage == NULL) {
            break;
        }
        if (FAILED(StringCchCopyA(errorMessage, 16, "Failed to load "))) {
            break;
        }
        if (FAILED(StringCchCopyA(errorMessage + 15, libraryNameLength + 1, libraryFilePath))) {
            break;
        }
        MessageBoxA(NULL, errorMessage, "Proxy Error", MB_ICONSTOP);
        HeapFree(processHeap, 0, errorMessage);
    } while (0);
    ExitProcess(-2);
}

static FARPROC PASCAL GetOriginalProcedureAddress(LPCSTR procedureName) {
    if (module != NULL) {
        FARPROC address = GetProcAddress(module, procedureName);
        if (address != NULL) {
            return address;
        }
    }
    HANDLE processHeap = GetProcessHeap();
    do {
        SIZE_T procedureNameLength = 5;
        if (HIWORD(procedureName) != 0) {
            if (FAILED(StringCchLengthA(procedureName, STRSAFE_MAX_CCH, &procedureNameLength))) {
                break;
            }
        }
        LPSTR errorMessage = (LPSTR) HeapAlloc(processHeap, 0, procedureNameLength + 26);
        if (errorMessage == NULL) {
            break;
        }
        if (FAILED(StringCchCopyA(errorMessage, 26, "Failed to load procedure "))) {
            break;
        }
        if (HIWORD(procedureName) == 0) {
            if (wsprintfA(errorMessage + 25, "#%04X", (WORD) procedureName) != 5) {
                break;
            }
        } else {
            if (FAILED(StringCchCopyA(errorMessage + 25, procedureNameLength + 1, procedureName))) {
                break;
            }
        }
        MessageBoxA(NULL, errorMessage, "Proxy Error", MB_ICONSTOP);
        HeapFree(processHeap, 0, errorMessage);
    } while (0);
    ExitProcess(-2);
}

void PASCAL ProxyLibrary_Load() {
    LoadOriginalLibrary(libraryFilePath);
    for (SIZE_T i = 0; i < symbolCount; i++) {
        symbolAddressArray[i] = GetOriginalProcedureAddress(symbolNameArray[i]);
    }
}

void PASCAL ProxyLibrary_Unload() {
    if (module != NULL) {
        FreeLibrary(module);
        module = NULL;
    }
}
