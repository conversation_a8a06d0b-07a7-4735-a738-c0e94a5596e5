# Patch/Keygen for Cracking Source Insight 4

🌍 **[简体中文](README.md) | [English](README-EN.md)**

This patch is used to automatically authorize Source Insight 4 without modifying any files.

## Usage

1. Install Source Insight 4.
2. Copy `msimg32.dll` to the installation directory of Source Insight 4.
3. Run Source Insight 4 to use it normally.

## Technology

This project uses [ProxyPESourceGenerator](https://github.com/YukiIsait/ProxyPESourceGenerator) to generate the source code for proxying `msimg32.dll`.

## Disclaimer

This project is for learning and research purposes only and should not be used for commercial or illegal purposes. If you like the program, please purchase a license to support the official software.

## License

This project is licensed under the MIT License. For details, see the [LICENSE](LICENSE.md) file.
