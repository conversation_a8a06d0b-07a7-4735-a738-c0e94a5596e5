name: Build And Release

on:
  push:
    tags:
      - "v*.*.*"

permissions:
  contents: write

jobs:
  build_and_release:
    runs-on: windows-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup MSBuild
        uses: microsoft/setup-msbuild@v2

      - name: Build
        run: msbuild -m -p:Configuration=Release -p:Platform=x86

      - name: Release
        uses: softprops/action-gh-release@v2
        with:
          fail_on_unmatched_files: true
          files: Release/msimg32.dll
